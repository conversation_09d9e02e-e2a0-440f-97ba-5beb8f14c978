import java.util.Scanner;
import java.util.HashMap;

public class LetterCounter {
    public static void main(String[] args) {
        Scanner input = new Scanner(System.in);
        HashMap<Character, Integer> letterFrequency = new HashMap<>();

        // Initialize the hashmap with alphabet letters
        for (char ch = 'A'; ch <= 'Z'; ch++) {
            letterFrequency.put(ch, 0);
        }

        // Process input
        String text = input.nextLine();
        text = text.replaceAll("[^a-zA-Z]", ""); // Filter non-alphabetic characters

        for (char ch : text.toCharArray()) {
            char uppercaseChar = Character.toUpperCase(ch);
            // Update the frequency count for the current character
            letterFrequency.put(uppercaseChar, letterFrequency.get(uppercaseChar) + 1);
        }

        input.close();

        // Build the result string
        StringBuilder result = new StringBuilder();
        for (char ch = 'A'; ch <= 'Z'; ch++) {
            int count = letterFrequency.get(ch);
            if (count > 0) {
                result.append(ch).append(":").append(count).append(" ");
            }
        }

        // Print result or "0" if no letters were counted
        if (result.length() == 0) {
            System.out.println("0");
        } else {
            System.out.println(result.toString().trim());
        }
    }
}
