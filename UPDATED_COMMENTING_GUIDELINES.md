# Updated Code Commenting Guidelines

## Core Principles

### What NOT to Comment
- **Simple imports** - No comments needed for standard library imports
- **Basic object initialization** - `Scanner scanner = new Scanner(System.in)` doesn't need explanation
- **Simple variable declarations** - `int maxSize = 0;` is self-explanatory
- **Obvious operations** - `scanner.close()` doesn't need "Close the scanner" comment
- **Standard getters/setters** - Simple accessor methods are self-documenting

### What TO Comment
- **Complex algorithms** - DFS, sorting logic, mathematical calculations
- **Business logic** - Why certain decisions are made, not just what is done
- **Non-obvious optimizations** - Memory-saving techniques, performance improvements
- **Important constraints** - Boundary conditions, assumptions, limitations
- **Core functionality** - The main purpose of complex methods/classes

## Examples

### ❌ Over-commented (Old Style)
```java
import java.util.Scanner; // Import Scanner for input reading
import java.util.Stack;   // Import Stack for DFS implementation

// Main class for the program
public class LargestIsland {
    private static int rows; // Number of rows in the map
    private static int cols; // Number of columns in the map
    
    // Main method where program starts
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in); // Create scanner object
        rows = scanner.nextInt(); // Read number of rows
        cols = scanner.nextInt(); // Read number of columns
```

### ✅ Appropriately commented (New Style)
```java
import java.util.Scanner;
import java.util.Stack;

public class LargestIsland {
    private static int rows;
    private static int cols;
    
    // Memory-optimized iterative DFS using coordinate encoding to avoid creating arrays
    private static int iterativeDfs(int r, int c) {
        Stack<Integer> stack = new Stack<>();
        stack.push(r * cols + c); // Encode coordinates as single integer
        
        while (!stack.isEmpty()) {
            int encoded = stack.pop();
            int currentR = encoded / cols; // Decode row
            int currentC = encoded % cols; // Decode column
```

## Implementation Rules

1. **Focus on "Why" not "What"** - Explain the reasoning behind complex decisions
2. **Comment complex algorithms** - DFS, parsing logic, data aggregation
3. **Explain optimizations** - Memory-saving techniques, performance improvements
4. **Keep it concise** - One line for simple explanations, multiple lines only for complex logic
5. **Update comments when code changes** - Ensure comments remain accurate

## Benefits

- **Cleaner code** - Less visual clutter from obvious comments
- **Better focus** - Comments highlight truly important information
- **Easier maintenance** - Fewer comments to update when code changes
- **Professional appearance** - Code looks more mature and well-structured
