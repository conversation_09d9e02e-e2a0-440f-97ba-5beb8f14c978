import java.math.BigInteger;

public class FactorialConverter {

    // Converts a number from factorial number system to decimal
    public static BigInteger convertFactorialToDecimal(String factorialNumber) {
        if (factorialNumber.equals("0")) {
            return BigInteger.ZERO;
        }
        
        String reversed = new StringBuilder(factorialNumber).reverse().toString();
        BigInteger result = BigInteger.ZERO;
        BigInteger currentFactorial = BigInteger.ONE; // Represents 0! = 1
        
        for (int position = 0; position < reversed.length(); position++) {
            char currentChar = reversed.charAt(position);
            int digitValue = convertCharToValue(currentChar);
            
            // Calculate the factorial for the current position (position + 1)!
            BigInteger positionFactorial = currentFactorial.multiply(BigInteger.valueOf(position + 1));
            
            // Update the result with the current digit's contribution
            result = result.add(BigInteger.valueOf(digitValue).multiply(positionFactorial));
            
            currentFactorial = positionFactorial;
        }
        
        return result;
    }

    // Converts a decimal number to factorial number system
    public static String convertDecimalToFactorial(BigInteger decimalNumber) {
        if (decimalNumber.equals(BigInteger.ZERO)) {
            return "0";
        }
        
        // Find the largest factorial less than or equal to the decimal number
        int maxFactorialIndex = findMaxFactorialIndex(decimalNumber);
        
        // Generate all necessary factorials
        BigInteger[] factorials = new BigInteger[maxFactorialIndex];
        factorials[0] = BigInteger.ONE; // 1!
        for (int i = 1; i < maxFactorialIndex; i++) {
            factorials[i] = factorials[i - 1].multiply(BigInteger.valueOf(i + 1));
        }
        
        // Build the factorial number representation
        StringBuilder factorialRepresentation = new StringBuilder();
        BigInteger remainder = decimalNumber;
        
        for (int i = maxFactorialIndex - 1; i >= 0; i--) {
            BigInteger currentFactorial = factorials[i];
            BigInteger[] divisionResult = remainder.divideAndRemainder(currentFactorial);
            int quotient = divisionResult[0].intValue();
            remainder = divisionResult[1];
            factorialRepresentation.append(convertValueToChar(quotient));
        }
        
        return factorialRepresentation.toString();
    }

    // Helper method to convert a character to its numerical value
    private static int convertCharToValue(char c) {
        if (Character.isDigit(c)) {
            return c - '0';
        } else {
            return 10 + (c - 'A');
        }
    }

    // Helper method to convert a numerical value to its character representation
    private static char convertValueToChar(int value) {
        if (value < 10) {
            return (char) ('0' + value);
        } else {
            return (char) ('A' + (value - 10));
        }
    }

    // Finds the largest factorial index needed for conversion
    private static int findMaxFactorialIndex(BigInteger target) {
        BigInteger factorial = BigInteger.ONE;
        int index = 1;
        
        while (true) {
            BigInteger nextFactorial = factorial.multiply(BigInteger.valueOf(index + 1));
            if (nextFactorial.compareTo(target) > 0) {
                break;
            }
            factorial = nextFactorial;
            index++;
        }
        
        return index;
    }

    public static void main(String[] args) {
        // Read input values
        String factorialInput = args[0];
        BigInteger decimalInput = new BigInteger(args[1]);
        
        // Perform conversions
        BigInteger decimalResult = convertFactorialToDecimal(factorialInput);
        String factorialResult = convertDecimalToFactorial(decimalInput);
        
        // Output results
        System.out.println(decimalResult);
        System.out.println(factorialResult);
    }
}