import java.util.*;

public class Labb3_1 {
    public static void main(String[] args) {
        Scanner input = new Scanner(System.in, "UTF-8");
        String text = input.nextLine(); // Read all the input

        // Split the input into parts
        String[] parts = text.split(";");
        String[] words = new String[parts.length];
        int[][] positions = new int[parts.length][];

        // Process each part
        for (int i = 0; i < parts.length; i++) {
            String[] keyValue = parts[i].trim().split(":");
            if (keyValue.length == 2) {
                words[i] = keyValue[0].trim();
                String[] posStrings = keyValue[1].trim().split("\\s+");
                positions[i] = new int[posStrings.length];
                for (int j = 0; j < posStrings.length; j++) {
                    positions[i][j] = Integer.parseInt(posStrings[j]);
                }
            }
        }

        // Use StringBuilder to build the result
        StringBuilder result = new StringBuilder();

        // Fill in the words
        for (int i = 0; i < words.length; i++) {
            for (int pos : positions[i]) {
                // Ensure the StringBuilder is long enough
                while (result.length() <= pos + words[i].length() - 1) {
                    result.append(' ');
                }
                // Insert the word at the correct position
                for (int j = 0; j < words[i].length(); j++) {
                    result.setCharAt(pos + j, words[i].charAt(j));
                }
            }
        }

        // Print the result
        System.out.println(result.toString().stripTrailing());

        input.close();
    }
}