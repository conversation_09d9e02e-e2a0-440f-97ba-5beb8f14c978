# Dokumentation: <PERSON><PERSON>rst<PERSON> ön (P5-<PERSON><PERSON>rstaön_DD1380)

Denna dokumentation beskriver implementeringen av lösningen till problemet "Största ön". Målet är att du ska kunna förstå kodens funktionalitet och logik för att på ett övertygande sätt presentera den.

## Problembeskrivning

Problemet går ut på att räkna ut hur stor den största sammanhängande ön är på ett sjökort. Sjökortet representeras av ett rutnät med tecknen '∼' för vatten och '@' för land. Två '@'-tecken tillhör samma ö om de angränsar till varandra rakt i sidled eller höjdled (diagonaler räknas inte).

**Indata:** Indata inleds med två tal, `x` (antal rader) och `y` (antal kolumner). Därefter följer `x` rader med `y` tecken i varje, best<PERSON><PERSON>e av '∼' och '@'.

**Utdata:** <PERSON>tt heltal som anger storleken på den största ön. Om ingen ö finns, ska svaret vara '0'.

## Lösningens Översikt

Lösningen använder en algoritm som kallas Depth-First Search (DFS) för att hitta och mäta storleken på öar i sjökortet. Huvudidén är att:

1.  Iterera igenom varje cell i sjökortet.
2.  Om en landcell ('@') hittas som inte redan har besökts (dvs. tillhör en ö som vi inte redan har räknat):
    *   Starta en DFS-sökning från denna cell.
    *   DFS-sökningen kommer att rekursivt besöka alla angränsande landceller som tillhör samma ö och markera dem som besökta.
    *   Storleken på den aktuella ön (antalet '@'-tecken) räknas under DFS-sökningen.
3.  Hålla reda på den största östorleken som hittills har påträffats.

Klassen `LargestIsland` innehåller all logik för detta.

## Funktionsspecifikationer

### Klass: `LargestIsland`

Denna klass innehåller logiken för att läsa in sjökortet, hitta öar och bestämma storleken på den största.

*   **Statiska Fält:**
    *   `private static int[] dx = {-1, 1, 0, 0};`: En array som representerar förändringen i radindex för att flytta sig upp (-1), ner (1), eller stanna kvar (0) i rutnätet.
    *   `private static int[] dy = {0, 0, -1, 1};`: En array som representerar förändringen i kolumnindex för att flytta sig vänster (-1), höger (1), eller stanna kvar (0) i rutnätet. Tillsammans med `dx` definierar dessa de fyra möjliga rörelseriktningarna (upp, ner, vänster, höger).
    *   `private static int rows`: Lagrar antalet rader i sjökortet.
    *   `private static int cols`: Lagrar antalet kolumner i sjökortet.
    *   `private static char[][] grid`: En 2D-array av tecken som representerar själva sjökortet.
    *   `private static boolean[][] visited`: En 2D-array av booleans som används för att hålla reda på vilka landceller som redan har besökts under DFS-sökningen. Detta förhindrar att samma ö räknas flera gånger eller att programmet hamnar i en oändlig loop.

*   **Metoder:**
    *   `private static int dfs(int r, int c)`
        *   **Beskrivning:** Utför en Depth-First Search (DFS) från en given startcell (`r`, `c`) för att hitta alla sammanhängande landceller som tillhör samma ö. Metoden är rekursiv.
        *   **Parametrar:**
            *   `r`: Radindex för startcellen.
            *   `c`: Kolumnindex för startcellen.
        *   **Returvärde:** Ett heltal som representerar storleken (antalet '@'-tecken) på den ö som hittades med start från (`r`, `c`).

    *   `public static void main(String[] args)`
        *   **Beskrivning:** Huvudmetoden där programmet startar. Den ansvarar för att:
            1.  Läsa in dimensionerna på sjökortet (rader och kolumner).
            2.  Läsa in själva sjökortet och lagra det i `grid`-arrayen.
            3.  Initiera `visited`-arrayen.
            4.  Iterera igenom varje cell i `grid`.
            5.  Om en obesökt landcell ('@') hittas, anropa `dfs` för att räkna storleken på den ön.
            6.  Hålla reda på och uppdatera `maxIslandSize`.
            7.  Skriva ut `maxIslandSize`.

## Kodgenomgång (Workflow)

Lösningen följer ett systematiskt tillvägagångssätt för att hitta den största ön:

### 1. Inläsning och Initialisering (`main`-metoden)

*   **Dimensioner:** Programmet börjar med att läsa in antalet rader (`rows`) och kolumner (`cols`) från de två första talen i indata.
*   **Rutnät (Grid):** Sjökortet (`grid`) och `visited`-arrayen skapas med de inlästa dimensionerna.
*   **Sjökortsdata:** Därefter läses varje rad av sjökortet in som en sträng och konverteras till en teckenarray som lagras i `grid`.
*   **Maximal ö-storlek:** En variabel `maxIslandSize` initieras till 0. Denna kommer att hålla reda på storleken på den största ön som hittills har hittats.

### 2. Iteration och Sökning efter Öar (`main`-metoden)

Programmet använder nästlade loopar för att gå igenom varje cell (`i` för rad, `j` för kolumn) i `grid`:

*   **Villkor för DFS-start:** För varje cell kontrolleras två saker:
    1.  Är cellen land (`grid[i][j] == '@'`)?
    2.  Har cellen *inte* redan besökts (`!visited[i][j]`)?
*   **Starta DFS:** Om båda villkoren är sanna, betyder det att vi har hittat en ny, obesökt del av en ö. Då anropas `dfs(i, j)`-metoden för att utforska och räkna storleken på just denna ö.
*   **Uppdatera maximal storlek:** Returvärdet från `dfs` (storleken på den just analyserade ön) jämförs med `maxIslandSize`. Om den nya ön är större, uppdateras `maxIslandSize`.

### 3. Depth-First Search (`dfs`-metoden)

Denna rekursiva metod är kärnan i ö-sökningen:

*   **Markera som besökt:** När `dfs` anropas för en cell (`r`, `c`), markeras den omedelbart som besökt (`visited[r][c] = true`). Detta är avgörande för att undvika att räkna samma cell flera gånger och för att förhindra oändliga loopar i cykliska strukturer (även om det inte är ett problem här med enkla öar).
*   **Räkna aktuell cell:** Storleken på den aktuella ön (`currentIslandSize`) initieras till 1 (för den cell som `dfs` just startade från).
*   **Utforska grannar:** Metoden loopar sedan igenom de fyra möjliga riktningarna (upp, ner, vänster, höger) med hjälp av `dx`- och `dy`-arrayerna:
    *   För varje granne (`newR`, `newC`) kontrolleras följande:
        1.  **Inom gränserna:** Ligger grannen inom sjökortets dimensioner?
        2.  **Är land:** Är grannen en landcell (`grid[newR][newC] == '@'`)?
        3.  **Obesökt:** Har grannen inte redan besökts (`!visited[newR][newC]`)?
    *   **Rekursivt anrop:** Om alla dessa villkor är uppfyllda, görs ett rekursivt anrop till `dfs(newR, newC)`. Storleken som returneras från detta rekursiva anrop (dvs. storleken på den del av ön som nås via denna granne) adderas till `currentIslandSize`.
*   **Returnera ö-storlek:** När alla möjliga vägar från den ursprungliga cellen (`r`, `c`) har utforskats, returnerar `dfs`-metoden den totala `currentIslandSize` för den sammanhängande ön.

### 4. Utskrift (`main`-metoden)

När iterationen genom hela `grid` är klar, kommer `maxIslandSize` att innehålla storleken på den största ön som hittades (eller 0 om inga öar fanns). Detta värde skrivs sedan ut.

Detta tillvägagångssätt garanterar att varje landcell besöks högst en gång och att alla sammanhängande delar av en ö korrekt identifieras och räknas.

