import java.util.Scanner;
import java.util.Stack;

// Huvudklassen för programmet som räknar ut storleken på den största ön.
public class LargestIsland {

    // dx och dy definierar de fyra möjliga rörelseriktningarna (upp, ner, vänster, höger) i rutnätet.
    private static int[] dx = {-1, 1, 0, 0};
    private static int[] dy = {0, 0, -1, 1};

    private static int rows; // Antal rader i sjökortet.
    private static int cols; // Antal kolumner i sjökortet.
    private static char[][] grid; // Sjökortet som ett rutnät av tecken.
    private static boolean[][] visited; // Håller reda på besökta landceller.

    // Utför en iterativ Depth-First Search (DFS) för att hitta alla sammanhängande landceller i en ö.
    // Denna metod returnerar storleken på den ö som startades från.
    // r: Rad-index för startpunkten.
    // c: Kolumn-index för startpunkten.
    private static int iterativeDfs(int r, int c) {
        // Använder en stack för att hantera celler som ska besökas. Detta undviker rekursion och därmed StackOverflowError
        // för stora kartor, vilket var problemet tidigare.
        Stack<int[]> stack = new Stack<>();
        stack.push(new int[]{r, c}); // Lägg till startcellen på stacken.
        visited[r][c] = true; // Markera startcellen som besökt för att inte besöka den igen.
        int currentIslandSize = 0; // Räknare för öns storlek, initieras till noll.

        // Fortsätt så länge det finns celler att utforska på stacken.
        while (!stack.isEmpty()) {
            int[] current = stack.pop(); // Hämta nästa cell att besöka från stacken.
            int currentR = current[0];
            int currentC = current[1];
            currentIslandSize++; // Öka öns storlek med 1 för den nu besökta cellen.

            // Utforska grannar i de fyra riktningarna (upp, ner, vänster, höger).
            for (int i = 0; i < 4; i++) {
                int newR = currentR + dx[i];
                int newC = currentC + dy[i];

                // Kontrollera om grannen är giltig att besöka:
                // 1. newR och newC måste vara inom sjökortets gränser.
                // 2. grid[newR][newC] måste vara land (
                // 3. visited[newR][newC] måste vara false (cellen får inte ha besökts tidigare).
                if (newR >= 0 && newR < rows && newC >= 0 && newC < cols &&
                    grid[newR][newC] == '@' && !visited[newR][newC]) {
                    
                    visited[newR][newC] = true; // Markera grannen som besökt.
                    stack.push(new int[]{newR, newC}); // Lägg till grannen på stacken för framtida utforskning.
                }
            }
        }
        return currentIslandSize; // Returnera öns totala storlek efter att alla sammanhängande landceller har hittats.
    }

    // Huvudmetoden där programmet startar.
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        // Läs in sjökortets dimensioner (rader och kolumner).
        rows = scanner.nextInt();
        cols = scanner.nextInt();
        scanner.nextLine(); // Konsumera radbrytningen efter att ha läst in heltalen.

        // Initiera rutnätet (sjökortet) och arrayen för besökta celler med de inlästa dimensionerna.
        grid = new char[rows][cols];
        visited = new boolean[rows][cols];

        // Läs in sjökortets data rad för rad.
        for (int i = 0; i < rows; i++) {
            String line = scanner.nextLine();
            grid[i] = line.toCharArray(); // Konvertera strängen till en teckenarray.
        }

        int maxIslandSize = 0; // Variabel för att lagra storleken på den största ön som hittas.

        // Iterera genom varje cell i sjökortet för att hitta och mäta öar.
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                // Om en obesökt landcell (
                if (grid[i][j] == '@' && !visited[i][j]) {
                    // Starta en iterativ DFS från denna punkt för att hitta hela ön och beräkna dess storlek.
                    int currentIslandSize = iterativeDfs(i, j);
                    // Uppdatera maxIslandSize om den nuvarande ön är större än den tidigare största.
                    if (currentIslandSize > maxIslandSize) {
                        maxIslandSize = currentIslandSize;
                    }
                }
            }
        }

        System.out.println(maxIslandSize); // Skriv ut storleken på den största ön.

        scanner.close(); // Stäng Scanner-objektet för att frigöra resurser.
    }
}


