import java.util.Scanner;
import java.util.Stack;

public class LargestIsland {

    
    private static int[] dx = {-1, 1, 0, 0};
    private static int[] dy = {0, 0, -1, 1};

    private static int rows;
    private static int cols;
    private static char[][] grid;
    private static boolean[][] visited;

    // Memory-optimized iterative DFS using coordinate encoding to avoid creating arrays
    private static int iterativeDfs(int r, int c) {
        Stack<Integer> stack = new Stack<>();
        stack.push(r * cols + c); // Encode coordinates as single integer
        visited[r][c] = true;
        int currentIslandSize = 0;

        while (!stack.isEmpty()) {
            int encoded = stack.pop();
            int currentR = encoded / cols; // Decode row
            int currentC = encoded % cols; // Decode column
            currentIslandSize++;

            for (int i = 0; i < 4; i++) {
                int newR = currentR + dx[i];
                int newC = currentC + dy[i];

                if (newR >= 0 && newR < rows && newC >= 0 && newC < cols &&
                    grid[newR][newC] == '@' && !visited[newR][newC]) {

                    visited[newR][newC] = true;
                    stack.push(newR * cols + newC); // Encode coordinates
                }
            }
        }
        return currentIslandSize;
    }

    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            rows = scanner.nextInt();
            cols = scanner.nextInt();
            scanner.nextLine();

            grid = new char[rows][cols];
            visited = new boolean[rows][cols];

            for (int i = 0; i < rows; i++) {
                String line = scanner.nextLine();
                grid[i] = line.toCharArray();
            }

            int maxIslandSize = 0;

            // Find all islands and track the largest one
            for (int i = 0; i < rows; i++) {
                for (int j = 0; j < cols; j++) {
                    if (grid[i][j] == '@' && !visited[i][j]) {
                        int currentIslandSize = iterativeDfs(i, j);
                        if (currentIslandSize > maxIslandSize) {
                            maxIslandSize = currentIslandSize;
                        }
                    }
                }
            }

            System.out.println(maxIslandSize);
        }
    }
}


