import java.util.Scanner;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.nio.charset.StandardCharsets;

public class Dekompression {
    public static void main(String[] args) {
        HashMap<String, List<Integer>> TextSeries = new HashMap<>();
        Scanner input = new Scanner(System.in, StandardCharsets.UTF_8);
        String text = input.nextLine(); // Read all the input
        //System.out.println(text); // Debugging line

        // Loop through all the series of text-part that is separated by ; and add it to the hashmap
        String[] parts = text.split(";");
        for (String part : parts) {
            part = part.trim();
            if (!part.isEmpty()) {
                String[] keyValue = part.split(":"); // Split the key-value pair by : and trim any leading/trailing spaces
                // Ensure that the key-value pair has exactly two parts (key and value) and that both parts are not empty or only contain spaces
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                   String[] values = keyValue[1].trim().split("\\s+"); // Split the value by spaces and trim any leading/trailing spaces
                    List<Integer> intValues = new ArrayList<>();// Create a list to store the integer values
                    for (String value : values) {
                        intValues.add(Integer.parseInt(value));
                    }
                    TextSeries.put(key, intValues);
                }
            }
        }

        // Process the text series
        //for (int value = 0 : intValues) {}

        // Print the HashMap for debugging (you can remove this later)
        System.out.println("TextSeries: " + TextSeries);

        // Decompress the text series


        input.close();
    }
}