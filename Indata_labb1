// Indata labb 1
2147483648

// Indata Labb 3
sju: 0 7; sj<PERSON>: 4 13; ka: 10; män: 16

h,: 18 24 51 57 84 90 96; h!: 30 63 102; She loves you,: 0 33 66; <PERSON><PERSON>: 15 21 27 48 54 60 81 87 93 99;

1.We don't calculate the maximum index beforehand.
2.We use a StringBuilder instead of a char array to build the result.
3.We dynamically extend the StringBuilder as needed when inserting words.
4.We directly insert characters into the StringBuilder at the correct positions.


import java.util.Scanner;
import java.util.HashMap;

public class Dekompression(){
    public static void main(String[] args){
        Scanner input = new Scanner(System.in);

        input.close();
    }
}