
import java.nio.charset.StandardCharsets;
import java.util.*;
public class Labb3 {
    /**
     * The main method of the program that processes input text and reconstructs a message.
     * It reads input in the format of "word:positions;", where positions are space-separated integers,
     * and reconstructs the original message by placing each word at its specified positions.
     */
    public static void main(String[] args) {
        Map<String, List<Integer>> TextSeries = new LinkedHashMap<>();
        Scanner input = new Scanner(System.in, StandardCharsets.UTF_8);
        String text = input.nextLine(); // Read all the input

        // Process the input text
        Arrays.stream(text.split(";")) //Uses Arrays.stream() to create a stream of strings by splitting by ; EX: "word1:1 2 3; word2:4 5 6" -> "word1", "word2")
              .map(String::trim) // map each string to a trimmed string
              .filter(part -> !part.isEmpty()) // Filter out empty or only containing spaces parts
              .forEach(part -> { // Process each part (word:positions)
                  String[] keyValue = part.split(":"); // Split the key-value pair by
                  if (keyValue.length == 2) { // Ensure that the key-value pair has exactly two parts (key and value) and that both parts are not empty or only contain spaces
                      String key = keyValue[0].trim(); // Accessed the key and declaring its data types
                      List<Integer> values = Arrays.stream(keyValue[1].trim().split("\\s+")) //Array.stream() is used to create a valid stream of integers from the space-separated values
                                                   .map(Integer::parseInt)// Map the values to integers and collect them into a list
                                                   .collect(java.util.stream.Collectors.toList()); // Collect the list into a List<Integer> using Collectors.toList()
                      TextSeries.put(key, values);
                  }
              });

        // Use StringBuilder to dynamically build the result
        StringBuilder result = new StringBuilder(); // Initialize a StringBuilder, a class for mutable strings in Java
        TextSeries.forEach((key, positions) -> { // Process each word and its positions
            for (int pos : positions) { // Loop through all the positions in the positions list given the word
                // Ensure the StringBuilder is long enough
                while (result.length() <= pos + key.length() - 1) { // The condition statement: as long as the StringBuilder's length is less than or equal to the current position plus the length of the word, append a space to the StringBuilder'
                    result.append(' ');
                }
                // Insert the word at the correct position
                for (int i = 0; i < key.length(); i++) { // Loop through all the characters in the word and insert them at the corresponding positions in the StringBuilder
                    result.setCharAt(pos + i, key.charAt(i)); // Set the character at the current position to the current character in the word
                }
            }
        });

        // Print the result
        System.out.println(result.toString().stripTrailing()); // Use the stripTrailing() method to remove any trailing spaces from the result and print it

        input.close();
    }
}