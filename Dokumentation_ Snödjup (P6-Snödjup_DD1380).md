# Dokumentation: Sn<PERSON>dju<PERSON> (P6-Snödjup_DD1380)

Denna dokumentation beskriver implementeringen av lösningen till problemet "Snödjup". Målet är att du ska kunna förstå kodens funktionalitet och logik för att på ett övertygande sätt presentera den.

## Problembeskrivning

Problemet går ut på att, från en lista med snödjupsmätningar från olika platser och datum, sammanställa en rekordlista. För varje år ska de fem platser som haft störst snödjup det året listas i fallande ordning. Om två platser hade samma snödjup ska de listas i alfabetisk ordning efter ortnamnet.

**Indata:** En lista med rader, där varje rad innehåller `<datum> <ortnamn> <snödjup i meter>`. Datumet är på formen YYYYMMDD, ortnamnet kan vara ett eller flera ord, och snödjupet är ett decimaltal med punkt som decimaltecken.

**Utdata:** <PERSON><PERSON><PERSON> varje å<PERSON>, först året på en egen rad, sedan en lista med högst fem orter med störst snödjup för det året. Formatet för varje ort är `<ortsnamn> <snödjup i meter>` (med en decimal).

## Lösningens Översikt

Lösningen är uppdelad i två huvudklasser:

1.  `SnowDepthData`: En hjälpklass som representerar en enskild snödjupsmätning. Den lagrar datum, plats, snödjup och år. Den är också `Comparable`, vilket betyder att den vet hur den ska jämföras med andra `SnowDepthData`-objekt (för sortering).
2.  `SnowDepth`: Huvudklassen som läser in data, bearbetar den och skriver ut resultatet. Den använder en `HashMap` för att effektivt lagra och organisera snödjupsdata per år och plats, och säkerställer att endast det högsta snödjupet för varje plats per år sparas.

## Funktionsspecifikationer

### Klass: `SnowDepthData`

Denna klass kapslar in informationen för en enskild snödjupsmätning och definierar hur dessa mätningar ska jämföras och hanteras.

*   **Fält:**
    *   `private LocalDate date`: Datumet för mätningen.
    *   `private String location`: Platsen där mätningen gjordes.
    *   `private double depth`: Snödjupet i meter.
    *   `private int year`: Året för mätningen, utdraget från `date`.

*   **Konstruktor:**
    *   `public SnowDepthData(String dateString, String location, double depth)`
        *   **Beskrivning:** Skapar ett nytt `SnowDepthData`-objekt. Tolkar `dateString` till ett `LocalDate`-objekt och extraherar året.
        *   **Parametrar:**
            *   `dateString`: Datumet som en sträng (YYYYMMDD).
            *   `location`: Platsens namn.
            *   `depth`: Snödjupet som ett decimaltal.

*   **Metoder:**
    *   `public int getYear()`
        *   **Beskrivning:** Returnerar året för mätningen.
    *   `public String getLocation()`
        *   **Beskrivning:** Returnerar platsnamnet för mätningen.
    *   `public double getDepth()`
        *   **Beskrivning:** Returnerar snödjupet för mätningen.
    *   `@Override public int compareTo(SnowDepthData other)`
        *   **Beskrivning:** Definierar sorteringsordningen för `SnowDepthData`-objekt. Sorterar först efter `depth` i fallande ordning (störst djup först). Om djupen är lika, sorterar den efter `location` i alfabetisk ordning (stigande).
    *   `@Override public boolean equals(Object o)`
        *   **Beskrivning:** Definierar när två `SnowDepthData`-objekt anses vara lika. Två objekt är lika om de har samma `year` och `location`. Detta är viktigt för att `HashMap` ska kunna hantera unika platser per år korrekt.
    *   `@Override public int hashCode()`
        *   **Beskrivning:** Genererar en hashkod för objektet, baserat på `location` och `year`. Nödvändig för att `equals` ska fungera korrekt med `HashMap`.
    *   `@Override public String toString()`
        *   **Beskrivning:** Returnerar en strängrepresentation av objektet i formatet "Plats Djup" (t.ex. "Kiruna 1.2"), med snödjupet formaterat till en decimal.

### Klass: `SnowDepth`

Detta är huvudprogrammet som hanterar inläsning, bearbetning och utskrift av snödjupsdata.

*   **Metoder:**
    *   `public static void main(String[] args)`
        *   **Beskrivning:** Huvudmetoden där programmet startar. Den läser in data, organiserar den, sorterar den och skriver ut resultatet enligt problembeskrivningen.

## Kodgenomgång (Workflow)

Lösningen följer ett tydligt arbetsflöde för att hantera snödjupsdata:

### 1. Inläsning och Parsning av Data

Programmet börjar med att läsa in indata rad för rad med hjälp av en `Scanner`. Varje rad representerar en snödjupsmätning. Raden delas upp i tre huvuddelar: datum, plats och snödjup.

*   **Hantering av platsnamn med flera ord:** En viktig detalj är att platsnamnet kan bestå av flera ord (t.ex. "Val d'Isere"). Koden hanterar detta genom att dela upp raden i delar baserat på mellanslag och sedan bygga ihop platsnamnet från de delar som ligger mellan datumet (första delen) och snödjupet (sista delen).
*   **Skapande av `SnowDepthData`-objekt:** För varje inläst rad skapas ett `SnowDepthData`-objekt. Detta objekt kapslar in all relevant information för den specifika mätningen.

### 2. Lagring och Aggregering av Data

All inläst data lagras i en komplex datastruktur: `Map<Integer, Map<String, SnowDepthData>> yearlyLocationData`.

*   **Yttre `HashMap` (per år):** Den yttre `HashMap`-en använder året (`Integer`) som nyckel. Detta gör det enkelt att gruppera all data per år.
*   **Inre `HashMap` (per plats):** För varje år finns en inre `HashMap`. Denna inre `HashMap` använder platsnamnet (`String`) som nyckel och ett `SnowDepthData`-objekt som värde. Detta är avgörande för att hantera kravet att bara det *högsta* snödjupet för en given plats under ett specifikt år ska sparas.
*   **`compute` Metoden för Aggregering:** När ett nytt `SnowDepthData`-objekt läses in, används `compute` metoden på den inre `HashMap`-en. Denna metod kontrollerar om det redan finns data för den aktuella platsen under det aktuella året. Om det inte finns någon data, eller om det nya snödjupet är större än det befintliga, uppdateras posten med det nya (högre) snödjupet. Annars behålls den befintliga posten. Detta säkerställer att vi alltid har det maximala snödjupet för varje plats per år.

### 3. Bearbetning och Utskrift av Resultat

När all data har lästs in och aggregerats, bearbetas och skrivs resultatet ut:

*   **Sortering av år:** Först hämtas alla unika år som det finns data för, och dessa sorteras i stigande ordning (kronologiskt). Detta säkerställer att utskriften sker år för år.
*   **Iteration över år:** Programmet loopar sedan igenom varje sorterat år.
*   **Hämtning och sortering av data per år:** För varje år hämtas alla `SnowDepthData`-objekt som har aggregerats för det året. Dessa objekt läggs i en `ArrayList` och sorteras sedan. Sorteringen sker automatiskt enligt `compareTo`-metoden i `SnowDepthData`-klassen, vilket innebär att de sorteras efter snödjup i fallande ordning, och sedan alfabetiskt efter platsnamn om snödjupen är lika.
*   **Utskrift av topp 5:** Slutligen loopar programmet igenom den sorterade listan för det aktuella året och skriver ut de fem första posterna (de fem djupaste snödjupen). Varje post formateras som "Plats Djup" med en decimal, precis som specificerat i problembeskrivningen.

Detta arbetsflöde säkerställer att alla krav i problembeskrivningen uppfylls, från inläsning och aggregering till sortering och formaterad utskrift av resultatet.

