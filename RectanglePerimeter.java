import java.util.Scanner;

public class RectanglePerimeter {
    /**
     * Main method to calculate the minimum perimeter of a rectangle
     * given an area A, where the sides are integer lengths.
     */
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        long area = scanner.nextLong(); // Read the area as a long integer
        long minimumPerimeter = Long.MAX_VALUE; // Initialize minimum perimeter

        // Iterate through possible side lengths up to the square root of area
        for (long length = 1; length * length <= area; length++) {
            if (area % length == 0) {  // Check if length is a divisor of area
                long width = area / length; // Calculate the corresponding width
                long perimeter = 2 * (length + width); // Calculate the perimeter
                minimumPerimeter = Math.min(minimumPerimeter, perimeter); // Update minimum perimeter
            }
        }
        // Print the minimum perimeter found
        System.out.println(minimumPerimeter);
    }
}