# Dokumentation: Faktoradiska tal (P4-Faktoradiskatal_DD1380)

Denna dokumentation beskriver implementeringen av lösningen till problemet "Faktoradiska tal". Målet är att du ska kunna förstå kodens funktionalitet och logik för att på ett övertygande sätt presentera den.

## Problembeskrivning

Problemet handlar om att konvertera tal mellan det vanliga decimalsystemet och ett "fakultetstalssystem". I ett fakultetstalssystem har varje position i talet en bas som är en fakultet (1!, 2!, 3!, osv.).

Om ett fakultetstal `F` är uppbyggt av `n` fakulteter, kan det konverteras till ett decimaltal `D` med formeln:

`D = ∑ (f_i * i!)`

där `f_i` är den `i`:e siffran i talet `F` (räknat från höger), och `f_i ≤ i`. <PERSON><PERSON>r siffror större än 9 används bokstäverna A-Z (A=10, B=11, osv.).

**Indata:** Två tal på varsin rad. Det första talet är ett fakultetstal (sträng), och det andra är ett decimaltal (heltal).

**Utdata:** Två tal på varsin rad. Det första är det inlästa fakultetstalet konverterat till decimaltal, och det andra är det inlästa decimaltalet konverterat till fakultetstal.

## Lösningens Översikt

Lösningen består av en enda klass, `Factoradic`, som innehåller metoder för att utföra konverteringarna åt båda hållen:

1.  `factoradicToDecimal`: Konverterar en sträng som representerar ett fakultetstal till dess decimala motsvarighet.
2.  `decimalToFactoradic`: Konverterar ett decimaltal till dess motsvarande fakultetstal som en sträng.

Eftersom fakultetstal och decimaltal kan bli mycket stora, används `java.math.BigInteger` för att hantera dessa tal, vilket förhindrar problem med overflow som kan uppstå med vanliga `int` eller `long` datatyper.

## Funktionsspecifikationer

### Klass: `Factoradic`

Denna klass innehåller logiken för konvertering mellan decimaltal och fakultetstal.

*   **Metoder:**
    *   `public static BigInteger factoradicToDecimal(String factoradicStr)`
        *   **Beskrivning:** Konverterar ett fakultetstal, givet som en sträng, till dess decimala motsvarighet. Hanterar både numeriska siffror (0-9) och alfabetiska siffror (A-Z för 10-35).
        *   **Parametrar:**
            *   `factoradicStr`: Strängen som representerar fakultetstalet (t.ex. "654320", "32A40244706404200").
        *   **Returvärde:** Ett `BigInteger`-objekt som representerar det konverterade decimaltalet.

    *   `public static String decimalToFactoradic(BigInteger decimal)`
        *   **Beskrivning:** Konverterar ett decimaltal till dess motsvarande fakultetstal som en sträng. Använder en algoritm baserad på upprepad division och restberäkning.
        *   **Parametrar:**
            *   `decimal`: Ett `BigInteger`-objekt som representerar decimaltalet som ska konverteras.
        *   **Returvärde:** En sträng som representerar det konverterade fakultetstalet.

    *   `public static void main(String[] args)`
        *   **Beskrivning:** Huvudmetoden där programmet startar. Den läser in de två talen från standard input, anropar de relevanta konverteringsmetoderna och skriver ut resultaten till standard output.

## Kodgenomgång (Workflow)

Programmet utför två huvudsakliga arbetsflöden: konvertering från fakultetstal till decimaltal och vice versa.

### 1. Konvertering från Fakultetstal till Decimaltal (`factoradicToDecimal`)

Detta arbetsflöde följer den matematiska definitionen `D = ∑ (f_i * i!)`.

*   **Initialisering:**
    *   `decimal` initieras till `0` (det ackumulerade decimaltalet).
    *   `factorial` initieras till `1` (representerar 0! eller 1! beroende på hur man ser det, men det är startvärdet för multiplikationerna).
*   **Iteration:** Metoden loopar igenom fakultetstalets siffror från *höger till vänster*. Detta är viktigt eftersom den högra siffran motsvarar 1!, nästa 2!, osv.
    *   **Sifferutvinning och konvertering:** För varje tecken i fakultetstalsträngen (från höger) extraheras siffran. Om det är en numerisk siffra (0-9) konverteras den direkt. Om det är en bokstav (A-Z) konverteras den till motsvarande numeriska värde (A=10, B=11, osv.).
    *   **Ackumulering:** Det utvunna siffervärdet multipliceras med den aktuella fakulteten (`factorial`) och läggs till det totala `decimal`-värdet.
    *   **Nästa fakultet:** `factorial` uppdateras för nästa iteration genom att multipliceras med `(i + 2)`. Detta säkerställer att vi korrekt beräknar 1!, 2!, 3!, osv., i varje steg av loopen.
*   **Resultat:** När loopen är klar, innehåller `decimal` det slutliga decimaltalet.

### 2. Konvertering från Decimaltal till Fakultetstal (`decimalToFactoradic`)

Detta arbetsflöde använder en algoritm baserad på upprepad division och restberäkning.

*   **Specialfall (0):** Om det inkommande decimaltalet är `0`, returneras strängen "0" direkt.
*   **Initialisering:**
    *   En `ArrayList` (`factoradicDigits`) skapas för att lagra de beräknade fakultetssiffrorna.
    *   `i` initieras till `2`. Detta är den första divisorn som används för att hitta den första fakultetssiffran (koefficienten för 1!).
*   **Iteration:** Loopen fortsätter så länge det `decimal`-värdet är större än `0`.
    *   **Restberäkning:** `remainder = decimal.remainder(i)` beräknar resten när `decimal` divideras med `i`. Denna rest är den aktuella fakultetssiffran (`f_k`).
    *   **Sifferkonvertering:** Resten konverteras till ett tecken. Om resten är mindre än 10, blir det en vanlig siffra (0-9). Annars konverteras det till en bokstav (A-Z).
    *   **Lägg till siffra:** Det konverterade tecknet läggs till i `factoradicDigits`-listan.
    *   **Division:** `decimal` uppdateras genom att divideras med `i` (`decimal = decimal.divide(i)`).
    *   **Nästa divisor:** `i` ökas med `1` (`i = i.add(BigInteger.ONE)`) för nästa iteration. Detta säkerställer att vi dividerar med 2, sedan 3, sedan 4, osv., för att hitta nästa fakultetssiffra.
*   **Omvänd ordning och sammanslagning:** Eftersom siffrorna samlas i omvänd ordning (från höger till vänster), vänds listan `factoradicDigits` om. Slutligen slås alla tecken i listan ihop till en enda sträng som representerar fakultetstalet.

### 3. Huvudprogram (`main`)

*   **Inläsning:** Programmet läser in två rader från standard input: först fakultetstalet som en sträng, sedan decimaltalet som en sträng som konverteras till `BigInteger`.
*   **Konvertering och utskrift:** Programmet anropar `factoradicToDecimal` med det inlästa fakultetstalet och skriver ut resultatet. Därefter anropas `decimalToFactoradic` med det inlästa decimaltalet och skriver ut dess resultat.

Detta strukturerade tillvägagångssätt säkerställer att båda konverteringsriktningarna hanteras korrekt och effektivt, även för mycket stora tal, tack vare användningen av `BigInteger`.

