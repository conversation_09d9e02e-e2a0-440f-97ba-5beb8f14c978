import java.math.BigInteger;

public class TestFactorialConverter {
    public static void main(String[] args) {
        // Test with the first sample input
        String factorialInput1 = "654320";
        BigInteger decimalInput1 = new BigInteger("25");
        
        // Perform conversions for first sample
        BigInteger decimalResult1 = FactorialConverter.convertFactorialToDecimal(factorialInput1);
        String factorialResult1 = FactorialConverter.convertDecimalToFactorial(decimalInput1);
        
        // Output results for first sample
        System.out.println("Test Case 1:");
        System.out.println(decimalResult1);
        System.out.println(factorialResult1);
        
        // Test with the second sample input
        String factorialInput2 = "32A40244706404200";
        BigInteger decimalInput2 = new BigInteger("1122334455667788");
        
        // Perform conversions for second sample
        BigInteger decimalResult2 = FactorialConverter.convertFactorialToDecimal(factorialInput2);
        String factorialResult2 = FactorialConverter.convertDecimalToFactorial(decimalInput2);
        
        // Output results for second sample
        System.out.println("\nTest Case 2:");
        System.out.println(decimalResult2);
        System.out.println(factorialResult2);
    }
}
