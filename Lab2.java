import java.util.Scanner;
import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.Arrays;
public class Lab2{
    public static void main(String[] args){
        List<Character> alphabets = Arrays.asList(
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
                'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');
        int counter = 0;
        Scanner sc = new Scanner (System.in);
        HashMap<String,Integer> CountOfLetter= new HashMap<String, Integer>();
        while (sc.hasNext()){
            String Words = sc.next().replaceAll("[^a-zA-Z]","");
            String[] letters = Words.split("");
            System.out.println(Words); //Debugging statement
            for (String letter:letters){
                String Alphabet=letter.toUpperCase();
                if(alphabets.contains(Alphabet)){
                    counter += 1;
                    CountOfLetter.put(Alphabet,counter);
                } else {
                    counter += 0;
                }
            }
        }
        sc.close();
        System.out.println(CountOfLetter);
    }

}
