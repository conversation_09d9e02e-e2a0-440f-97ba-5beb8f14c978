import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

// Huvudklassen för programmet som hanterar snödjupsdata.
public class SnowDepth {

    // Huvudmetoden som körs när programmet startar.
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // Karta för att lagra snödjupsdata per år och plats.
        // Yttre Map: År (Integer) -> Inre Map.
        // Inre Map: Platsnamn (String) -> SnowDepthData-objekt (högsta djup för platsen det året).
        Map<Integer, Map<String, SnowDepthData>> yearlyLocationData = new HashMap<>();

        // Läs in data rad för rad så länge det finns input.
        while (scanner.hasNextLine()) {
            String line = scanner.nextLine();
            if (line.trim().isEmpty()) {
                continue; // Hoppa över tomma rader.
            }
            
            // Parsar varje rad för att extrahera datum, plats och snödjup.
            // Hitta index för första och sista mellanslaget för att korrekt hantera platsnamn med flera ord.
            int firstSpaceIndex = line.indexOf(" ");
            int lastSpaceIndex = line.lastIndexOf(" ");

            String dateString = line.substring(0, firstSpaceIndex);
            String location = line.substring(firstSpaceIndex + 1, lastSpaceIndex);
            double depth = Double.parseDouble(line.substring(lastSpaceIndex + 1));

            // Skapa ett nytt SnowDepthData-objekt med den inlästa datan.
            SnowDepthData newData = new SnowDepthData(dateString, location, depth);
            
            // Aggregera data: För varje år och plats, spara endast det högsta snödjupet.
            // computeIfAbsent: Hämtar eller skapar en inre HashMap för det aktuella året.
            // compute: För den inre HashMapen, uppdaterar posten för platsen.
            // Om ny data har högre djup än befintlig, ersätts den. Annars behålls befintlig.
            yearlyLocationData.computeIfAbsent(newData.getYear(), k -> new HashMap<>())
                              .compute(location, (k, existingData) -> {
                                  if (existingData == null || newData.getDepth() > existingData.getDepth()) {
                                      return newData;
                                  } else {
                                      return existingData;
                                  }
                              });
        }
        scanner.close();

        // Hämta alla år med data och sortera dem kronologiskt.
        List<Integer> years = new ArrayList<>(yearlyLocationData.keySet());
        Collections.sort(years);

        // Iterera genom varje år för att skriva ut topp 5 snödjup.
        for (int year : years) {
            System.out.println(year);
            
            // Hämta alla unika snödjupsdata för året och sortera dem.
            // Sorteringen sker enligt compareTo-metoden i SnowDepthData-klassen (djupast först, sedan alfabetiskt).
            List<SnowDepthData> dataForYear = new ArrayList<>(yearlyLocationData.get(year).values());
            Collections.sort(dataForYear);

            int count = 0;
            // Skriv ut de fem första (djupaste) platserna för året.
            for (SnowDepthData data : dataForYear) {
                if (count < 5) {
                    System.out.println(data.getLocation() + " " + String.format("%.1f", data.getDepth()));
                    count++;
                } else {
                    break; // Avbryt loopen när 5 platser har skrivits ut.
                }
            }
        }
    }
}


