import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

public class SnowDepth {

    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            // Store max snow depth per location per year
            Map<Integer, Map<String, SnowDepthData>> yearlyLocationData = new HashMap<>();

            // Read and process input data
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine().trim();
                if (line.isEmpty()) {
                    continue;
                }

                // Improved parsing to handle multiple spaces and edge cases
                String[] parts = line.split("\\s+");
                if (parts.length < 3) {
                    continue; // Skip malformed lines
                }

                String dateString = parts[0];
                double depth = Double.parseDouble(parts[parts.length - 1]);

                // Location is everything between date and depth (handles multi-word locations)
                StringBuilder locationBuilder = new StringBuilder();
                for (int i = 1; i < parts.length - 1; i++) {
                    if (i > 1) locationBuilder.append(" ");
                    locationBuilder.append(parts[i]);
                }
                String location = locationBuilder.toString();

                SnowDepthData newData = new SnowDepthData(dateString, location, depth);

                // Keep only the maximum depth per location per year
                yearlyLocationData.computeIfAbsent(newData.getYear(), k -> new HashMap<>())
                                  .compute(location, (k, existingData) -> {
                                      if (existingData == null || newData.getDepth() > existingData.getDepth()) {
                                          return newData;
                                      } else {
                                          return existingData;
                                      }
                                  });
            }

            // Sort years chronologically and output results
            List<Integer> years = new ArrayList<>(yearlyLocationData.keySet());
            Collections.sort(years);

            for (int year : years) {
                System.out.println(year);

                // Sort locations by depth (descending) then alphabetically
                List<SnowDepthData> dataForYear = new ArrayList<>(yearlyLocationData.get(year).values());
                Collections.sort(dataForYear);

                // Output top 5 locations for the year
                int count = 0;
                for (SnowDepthData data : dataForYear) {
                    if (count < 5) {
                        System.out.println(data.getLocation() + " " + String.format("%.1f", data.getDepth()));
                        count++;
                    } else {
                        break;
                    }
                }
            }
        }
    }
}


