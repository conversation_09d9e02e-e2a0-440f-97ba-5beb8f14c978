import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

// Klass som representerar en snödjupsmätning och är jämförbar för sortering.
public class SnowDepthData implements Comparable<SnowDepthData> {
    private LocalDate date; // Datum för mätningen.
    private String location; // Platsen för mätningen.
    private double depth; // Snödjupet i meter.
    private int year; // Året för mätningen.

    // Konstruktor för att skapa ett SnowDepthData-objekt.
    public SnowDepthData(String dateString, String location, double depth) {
        this.date = LocalDate.parse(dateString, DateTimeFormatter.ofPattern("yyyyMMdd"));
        this.location = location;
        this.depth = depth;
        this.year = this.date.getYear();
    }

    // Returnerar året för mätningen.
    public int getYear() {
        return year;
    }

    // Returnerar platsen för mätningen.
    public String getLocation() {
        return location;
    }

    // Returnerar snödjupet för mätningen.
    public double getDepth() {
        return depth;
    }

    // Definierar sorteringsordningen: djup fallande, sedan plats alfabetiskt stigande.
    @Override
    public int compareTo(SnowDepthData other) {
        int depthComparison = Double.compare(other.depth, this.depth);
        if (depthComparison != 0) {
            return depthComparison;
        }
        return this.location.compareTo(other.location);
    }

    // Definierar när två SnowDepthData-objekt är lika (samma år och plats).
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SnowDepthData that = (SnowDepthData) o;
        return year == that.year &&
               Objects.equals(location, that.location);
    }

    // Genererar hashkod baserat på plats och år.
    @Override
    public int hashCode() {
        return Objects.hash(location, year);
    }

    // Returnerar en strängrepresentation av objektet (t.ex. "Kiruna 1.2").
    @Override
    public String toString() {
        return String.format("%s %.1f", location, depth);
    }
}


