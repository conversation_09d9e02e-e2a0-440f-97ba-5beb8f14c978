import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

public class SnowDepthData implements Comparable<SnowDepthData> {
    private LocalDate date;
    private String location;
    private double depth;
    private int year;

    public SnowDepthData(String dateString, String location, double depth) {
        this.date = LocalDate.parse(dateString, DateTimeFormatter.ofPattern("yyyyMMdd"));
        this.location = location;
        this.depth = depth;
        this.year = this.date.getYear();
    }

    public int getYear() {
        return year;
    }

    public String getLocation() {
        return location;
    }

    public double getDepth() {
        return depth;
    }

    // Sort by depth (descending), then by location (ascending)
    @Override
    public int compareTo(SnowDepthData other) {
        int depthComparison = Double.compare(other.depth, this.depth);
        if (depthComparison != 0) {
            return depthComparison;
        }
        return this.location.compareTo(other.location);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SnowDepthData that = (SnowDepthData) o;
        return year == that.year &&
               Objects.equals(location, that.location);
    }

    @Override
    public int hashCode() {
        return Objects.hash(location, year);
    }

    @Override
    public String toString() {
        return String.format("%s %.1f", location, depth);
    }
}


